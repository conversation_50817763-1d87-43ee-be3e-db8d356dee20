import { NextRequest, NextResponse } from 'next/server';
import { getWordNetService } from '@/backend/wire';
import { Language } from '@prisma/client';
import { withErrorHandling } from '@/lib/api-error-middleware';

async function GET(
	request: NextRequest,
	{ params }: { params: { term: string } }
): Promise<NextResponse> {
	const { term } = params;
	const { searchParams } = new URL(request.url);
	const language = (searchParams.get('language') as Language) || Language.EN;

	if (!term) {
		return NextResponse.json({ error: 'Term is required' }, { status: 400 });
	}

	try {
		const wordNetService = getWordNetService();
		const wordNetInfo = await wordNetService.getWordNetInfo(term, language);

		return NextResponse.json(wordNetInfo);
	} catch (error) {
		console.error('Error fetching WordNet data:', error);
		return NextResponse.json(
			{ error: 'Failed to fetch WordNet data' },
			{ status: 500 }
		);
	}
}

export { withErrorHandling(GET) as GET };
