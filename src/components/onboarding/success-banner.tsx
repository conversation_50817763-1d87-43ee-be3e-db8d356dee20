'use client';

import { <PERSON><PERSON>, Card, CardContent, Translate } from '@/components/ui';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircle2, RefreshCw, Target, Plus, X, ArrowRight, Trophy, Star } from 'lucide-react';
import Link from 'next/link';
import { useState, useEffect, useCallback } from 'react';

interface SuccessBannerProps {
	collectionId: string;
	addedWordsCount: number;
	totalWordsInCollection: number;
	onDismiss?: () => void;
}

export function SuccessBanner({
	collectionId,
	addedWordsCount,
	totalWordsInCollection,
	onDismiss,
}: SuccessBannerProps) {
	const [isVisible, setIsVisible] = useState(true);

	const handleDismiss = useCallback(() => {
		setIsVisible(false);
		onDismiss?.();
	}, [onDismiss]);

	// Removed auto-hide functionality - banner will only close when user clicks dismiss

	if (!isVisible || addedWordsCount === 0) return null;

	const isFirstWord = totalWordsInCollection === 1;
	const canTakeQuiz = totalWordsInCollection >= 10;
	const wordsNeededForQuiz = Math.max(0, 10 - totalWordsInCollection);

	const getMilestoneMessage = () => {
		if (isFirstWord) {
			return 'words.progress_milestone.first_word';
		}
		if (totalWordsInCollection === 10) {
			return 'words.progress_milestone.quiz_unlocked';
		}
		if (totalWordsInCollection % 5 === 0) {
			return 'words.progress_milestone.collection_growing';
		}
		return null;
	};

	const milestoneMessage = getMilestoneMessage();

	const nextSteps = [
		{
			icon: RefreshCw,
			titleKey: 'words.next_steps.review_words',
			descriptionKey: 'words.next_steps.review_description',
			href: `/collections/${collectionId}/vocabulary/review`,
			variant: 'default' as const,
			primary: true,
		},
		{
			icon: Plus,
			titleKey: 'words.next_steps.add_more_words',
			descriptionKey: 'words.next_steps.add_more_description',
			href: `/collections/${collectionId}/vocabulary/generate`,
			variant: 'outline' as const,
		},
		...(canTakeQuiz
			? [
					{
						icon: Target,
						titleKey: 'words.next_steps.take_quiz',
						descriptionKey: 'words.next_steps.quiz_description',
						href: `/collections/${collectionId}/vocabulary/mcq`,
						variant: 'outline' as const,
					},
			  ]
			: []),
	];

	return (
		<AnimatePresence>
			{isVisible && (
				<motion.div
					initial={{ opacity: 0, y: -50, scale: 0.95 }}
					animate={{ opacity: 1, y: 0, scale: 1 }}
					exit={{ opacity: 0, y: -50, scale: 0.95 }}
					transition={{ duration: 0.5, ease: 'easeOut' }}
					className="mb-6"
				>
					<Card className="relative overflow-hidden border-green-200 bg-gradient-to-br from-green-50 via-background to-green-50 dark:border-green-800 dark:from-green-900/20 dark:to-green-900/10">
						{/* Background decoration */}
						<div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-green-400/10 to-transparent rounded-full blur-2xl" />
						<div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-emerald-400/10 to-transparent rounded-full blur-xl" />

						{/* Dismiss button */}
						<Button
							variant="ghost"
							size="sm"
							onClick={handleDismiss}
							className="absolute top-4 right-4 h-8 w-8 p-0 hover:bg-green-100 dark:hover:bg-green-900/30"
						>
							<X className="h-4 w-4" />
						</Button>

						<CardContent className="relative p-6">
							<div className="flex items-start gap-4">
								{/* Success icon */}
								<div className="flex-shrink-0">
									<div className="p-3 rounded-2xl bg-green-100 border border-green-200 dark:bg-green-900/30 dark:border-green-800">
										<CheckCircle2 className="h-8 w-8 text-green-600 dark:text-green-400" />
									</div>
								</div>

								{/* Content */}
								<div className="flex-1 space-y-4">
									<div>
										<h3 className="text-xl font-bold text-green-800 dark:text-green-200 mb-2">
											<Translate text="words.success_banner.title" />
										</h3>
										<p className="text-green-700 dark:text-green-300 mb-1">
											<Translate
												text="words.success_banner.words_added"
												values={{ count: addedWordsCount }}
											/>
										</p>
										<p className="text-sm text-green-600 dark:text-green-400">
											<Translate text="words.success_banner.ready_to_review" />
										</p>
									</div>

									{/* Milestone message */}
									{milestoneMessage && (
										<div className="flex items-center gap-2 p-3 rounded-lg bg-green-100/50 border border-green-200/50 dark:bg-green-900/20 dark:border-green-800/50">
											{totalWordsInCollection === 10 ? (
												<Trophy className="h-5 w-5 text-amber-600" />
											) : (
												<Star className="h-5 w-5 text-green-600 dark:text-green-400" />
											)}
											<span className="text-sm font-medium text-green-800 dark:text-green-200">
												<Translate text={milestoneMessage} />
											</span>
										</div>
									)}

									{/* Next steps */}
									<div className="space-y-3">
										<h4 className="text-sm font-medium text-green-800 dark:text-green-200">
											<Translate text="words.next_steps.title" />
										</h4>
										<div className="flex flex-wrap gap-2">
											{nextSteps.map((step) => (
												<Link key={step.titleKey} href={step.href}>
													<Button
														variant={step.variant}
														size="sm"
														className={`
															flex items-center gap-2 transition-all duration-200
															${
																step.primary
																	? 'bg-green-600 hover:bg-green-700 text-white shadow-lg shadow-green-600/25 dark:bg-green-700 dark:hover:bg-green-600'
																	: 'border-green-300 text-green-700 hover:bg-green-100 dark:border-green-700 dark:text-green-300 dark:hover:bg-green-900/30'
															}
														`}
													>
														<step.icon className="h-4 w-4" />
														<Translate text={step.titleKey} />
														{step.primary && (
															<ArrowRight className="h-4 w-4" />
														)}
													</Button>
												</Link>
											))}
										</div>
									</div>

									{/* Quiz progress hint */}
									{!canTakeQuiz && wordsNeededForQuiz > 0 && (
										<div className="text-xs text-green-600 dark:text-green-400 flex items-center gap-1">
											<Target className="h-3 w-3" />
											<Translate
												text="words.next_steps.quiz_locked"
												values={{ count: wordsNeededForQuiz }}
											/>
										</div>
									)}
								</div>
							</div>
						</CardContent>
					</Card>
				</motion.div>
			)}
		</AnimatePresence>
	);
}
